<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>RK Player</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="verticalLayout">
    <item>
     <widget class="QWidget" name="videoWidget">
      <property name="minimumSize">
       <size>
        <width>0</width>
        <height>400</height>
       </size>
      </property>
      <property name="styleSheet">
       <string notr="true">background-color: black;</string>
      </property>
     </widget>
    </item>
    <item>
     <widget class="QWidget" name="controlWidget">
      <property name="maximumSize">
       <size>
        <width>16777215</width>
        <height>120</height>
       </size>
      </property>
      <layout class="QVBoxLayout" name="controlLayout">
       <item>
        <layout class="QHBoxLayout" name="progressLayout">
         <item>
          <widget class="QLabel" name="currentTimeLabel">
           <property name="text">
            <string>00:00</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QSlider" name="progressSlider">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="minimum">
            <number>0</number>
           </property>
           <property name="maximum">
            <number>100</number>
           </property>
           <property name="value">
            <number>0</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="totalTimeLabel">
           <property name="text">
            <string>00:00</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>0</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="buttonLayout">
         <item>
          <spacer name="leftSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="rewindButton">
           <property name="text">
            <string>⏪</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>16</pointsize>
            </font>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="playPauseButton">
           <property name="text">
            <string>▶️</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>60</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>18</pointsize>
            </font>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="fastForwardButton">
           <property name="text">
            <string>⏩</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>50</width>
             <height>40</height>
            </size>
           </property>
           <property name="font">
            <font>
             <pointsize>16</pointsize>
            </font>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="rightSpacer">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>
